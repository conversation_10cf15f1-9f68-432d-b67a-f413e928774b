"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingBot = void 0;
const playwright_1 = require("playwright");
const events_1 = require("events");
class TradingBot extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.browser = null;
        this.page = null;
        this.isRunning = false;
        this.priceHistory = [];
        this.trades = [];
        this.priceCheckInterval = null;
        this.config = config;
        this.status = {
            isRunning: false,
            currentPrice: 0,
            tradesCount: 0,
            winCount: 0,
            lossCount: 0,
            winRate: 0,
            totalProfit: 0,
            lastTrade: null
        };
    }
    async start() {
        if (this.isRunning) {
            throw new Error('Bot is already running');
        }
        try {
            this.log('info', 'Starting trading bot...');
            // Use existing browser if provided, otherwise launch new one
            if (this.config.existingBrowser && this.config.existingPage) {
                this.log('info', 'Using existing browser context...');
                this.browser = this.config.existingBrowser;
                this.page = this.config.existingPage;
                // Verify the page is still valid and on the correct URL
                try {
                    const currentUrl = this.page?.url();
                    if (currentUrl && !currentUrl.includes('pocketoption.com')) {
                        this.log('info', 'Navigating to Pocket Option...');
                        await this.page?.goto(this.config.pocketOptionUrl);
                        await this.page?.waitForLoadState('domcontentloaded', { timeout: 30000 });
                    }
                }
                catch (error) {
                    this.log('warn', 'Error checking page URL, proceeding anyway...');
                }
            }
            else {
                this.log('info', 'Launching new browser context...');
                // Launch persistent browser context
                if (this.config.userDataDir) {
                    const context = await playwright_1.chromium.launchPersistentContext(this.config.userDataDir, {
                        headless: this.config.headless,
                        viewport: null
                    });
                    this.browser = context;
                    // Get the first page or create a new one
                    const pages = context.pages();
                    this.page = pages.length > 0 ? pages[0] : await context.newPage();
                }
                else {
                    // Fallback to regular browser launch
                    const browser = await playwright_1.chromium.launch({
                        headless: this.config.headless
                    });
                    this.browser = browser;
                    this.page = await browser.newPage();
                }
                // Navigate to Pocket Option
                this.log('info', 'Navigating to Pocket Option...');
                await this.page?.goto(this.config.pocketOptionUrl);
                // Wait for initial page load with longer timeout
                this.log('info', 'Waiting for page to load... Please complete any captcha and sign in.');
                await this.page?.waitForLoadState('domcontentloaded', { timeout: 120000 }); // 2 minutes
                // Wait for user to sign in - look for trading interface elements
                this.log('info', 'Please sign in to your Pocket Option account. The bot will wait for you to complete login.');
                // Wait for trading interface to be available (this indicates successful login)
                try {
                    // Wait for any trading elements to appear first
                    await this.page?.waitForSelector('body, .main-content, .trading-interface, .header, .menu', {
                        timeout: 300000 // 5 minutes for login
                    });
                }
                catch (error) {
                    throw new Error('Setup timeout: Please ensure you are logged in to Pocket Option and on the trading page.');
                }
            }
            this.log('info', 'Setting up trading environment...');
            // Wait a bit for the page to fully load
            await this.page?.waitForTimeout(3000);
            // Check if already in demo mode before switching
            const isAlreadyDemo = await this.checkIfInDemoMode();
            if (isAlreadyDemo) {
                this.log('info', 'Already in demo mode. Skipping demo account switch.');
            }
            else {
                this.log('info', 'Not in demo mode. Switching to demo account...');
                await this.switchToDemoAccount();
            }
            // Try to select a trading asset
            await this.selectTradingAsset();
            // Wait for trading interface to be ready
            await this.waitForTradingInterface();
            this.log('info', 'Trading interface setup complete.');
            this.isRunning = true;
            this.status.isRunning = true;
            this.status.startTime = Date.now();
            // Start price monitoring
            this.startPriceMonitoring();
            this.log('info', 'Trading bot started successfully');
            this.emitStatusUpdate();
        }
        catch (error) {
            this.log('error', `Failed to start bot: ${error}`);
            await this.stop();
            throw error;
        }
    }
    async stop() {
        this.log('info', 'Stopping trading bot...');
        this.isRunning = false;
        this.status.isRunning = false;
        if (this.priceCheckInterval) {
            clearInterval(this.priceCheckInterval);
            this.priceCheckInterval = null;
        }
        // Only close browser if we created it (not using existing instance)
        if (this.browser && !this.config.existingBrowser) {
            await this.browser.close();
            this.browser = null;
            this.page = null;
        }
        else if (this.config.existingBrowser) {
            // Keep browser open but clear references
            this.browser = null;
            this.page = null;
            this.log('info', 'Browser instance kept open for reuse');
        }
        this.log('info', 'Trading bot stopped');
        this.emitStatusUpdate();
    }
    startPriceMonitoring() {
        this.priceCheckInterval = setInterval(async () => {
            try {
                await this.checkPrice();
            }
            catch (error) {
                this.log('error', `Price monitoring error: ${error}`);
            }
        }, 1000); // Check price every second
    }
    async checkPrice() {
        if (!this.page || !this.isRunning)
            return;
        try {
            // Check if page is still valid
            if (this.page.isClosed()) {
                this.log('warn', 'Page is closed, stopping price monitoring');
                return;
            }
            // Try multiple selectors for Pocket Option price display
            const priceSelectors = [
                // Common price display selectors
                '.current-price',
                '.price-value',
                '.asset-price',
                '.quote-value',
                '.price-display',
                '.current-quote',
                '.live-price',
                '.market-price',
                // Chart-related price selectors
                '.trading-chart .price',
                '.chart-price',
                '.price-line',
                '.last-price',
                // Data attributes
                '[data-testid="price"]',
                '[data-price]',
                '[data-current-price]',
                // Pocket Option specific patterns
                '.po-price',
                '.rate-value',
                '.quote-rate',
                '.instrument-price',
                '.ticker-price',
                // Generic fallbacks
                '*[class*="price"]:not([class*="button"]):not([class*="btn"])',
                '*[id*="price"]',
                '.price:not(.price-button)'
            ];
            let priceElement = null;
            for (const selector of priceSelectors) {
                priceElement = await this.page.$(selector);
                if (priceElement)
                    break;
            }
            if (!priceElement) {
                // Debug: Try to find any elements that might contain price information
                await this.debugPriceElements();
                this.log('warn', 'Price element not found. Please ensure you are on the trading page.');
                return;
            }
            const priceText = await priceElement.textContent();
            const currentPrice = parseFloat(priceText?.replace(/[^\d.-]/g, '') || '0');
            if (currentPrice > 0) {
                const previousPrice = this.status.currentPrice;
                const priceData = {
                    current: currentPrice,
                    previous: previousPrice,
                    timestamp: Date.now(),
                    trend: currentPrice > previousPrice ? 'up' : currentPrice < previousPrice ? 'down' : 'neutral',
                    change: currentPrice - previousPrice,
                    changePercent: previousPrice > 0 ? ((currentPrice - previousPrice) / previousPrice) * 100 : 0
                };
                this.priceHistory.push(priceData);
                // Keep only last 100 price points
                if (this.priceHistory.length > 100) {
                    this.priceHistory.shift();
                }
                this.status.currentPrice = currentPrice;
                this.emit('price-update', priceData);
                // Check if we should make a trade
                if (this.config.settings.autoTrade) {
                    await this.evaluateTradeOpportunity(priceData);
                }
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            // Handle specific errors
            if (errorMessage.includes('Execution context was destroyed')) {
                this.log('warn', 'Page navigation detected, waiting for page to stabilize...');
                // Wait a bit for navigation to complete
                await new Promise(resolve => setTimeout(resolve, 3000));
                return;
            }
            if (errorMessage.includes('Target closed')) {
                this.log('warn', 'Browser tab was closed');
                return;
            }
            this.log('error', `Error checking price: ${errorMessage}`);
        }
    }
    async evaluateTradeOpportunity(priceData) {
        // Simple trading logic based on price change threshold
        const { threshold, maxTrades } = this.config.settings;
        // Don't trade if we've reached max trades
        if (this.status.tradesCount >= maxTrades) {
            return;
        }
        // Check if price change exceeds threshold
        if (Math.abs(priceData.changePercent) >= threshold) {
            const direction = priceData.trend === 'up' ? 'high' : 'low';
            await this.executeTrade(direction, priceData);
        }
    }
    async executeTrade(direction, priceData) {
        if (!this.page)
            return;
        try {
            this.log('info', `Executing ${direction} trade at price ${priceData.current}`);
            // Try multiple selectors for Pocket Option trading buttons
            const buttonSelectors = direction === 'high'
                ? ['.btn-call', '.high-btn', '.call-btn', '.up-btn', '[data-direction="call"]', '.green-btn']
                : ['.btn-put', '.low-btn', '.put-btn', '.down-btn', '[data-direction="put"]', '.red-btn'];
            let button = null;
            for (const selector of buttonSelectors) {
                button = await this.page.$(selector);
                if (button) {
                    this.log('info', `Found ${direction} button with selector: ${selector}`);
                    break;
                }
            }
            if (!button) {
                this.log('error', `${direction} button not found. Please ensure you are on the trading interface.`);
                return;
            }
            await button.click();
            this.log('info', `Clicked ${direction} button`);
            // Create trade record
            const trade = {
                id: `trade_${Date.now()}`,
                timestamp: Date.now(),
                direction,
                amount: this.config.settings.tradeAmount,
                entryPrice: priceData.current,
                result: 'pending',
                duration: 60 // Default 1 minute trade
            };
            this.trades.push(trade);
            this.status.tradesCount++;
            this.status.lastTrade = trade;
            this.emit('trade-result', trade);
            this.emitStatusUpdate();
            // Wait for trade result (simplified - in real implementation, monitor DOM for result)
            setTimeout(() => {
                this.checkTradeResult(trade.id);
            }, trade.duration * 1000);
        }
        catch (error) {
            this.log('error', `Error executing trade: ${error}`);
        }
    }
    async checkTradeResult(tradeId) {
        // This is a simplified implementation
        // In reality, you'd monitor the DOM for trade results
        const trade = this.trades.find(t => t.id === tradeId);
        if (!trade)
            return;
        // Simulate random result for now (replace with actual DOM monitoring)
        const isWin = Math.random() > 0.5;
        trade.result = isWin ? 'win' : 'loss';
        trade.exitPrice = this.status.currentPrice;
        trade.profit = isWin ? trade.amount * 0.8 : -trade.amount; // 80% payout
        if (isWin) {
            this.status.winCount++;
        }
        else {
            this.status.lossCount++;
        }
        this.status.winRate = (this.status.winCount / this.status.tradesCount) * 100;
        this.status.totalProfit += trade.profit;
        this.status.lastTrade = trade;
        this.emit('trade-result', trade);
        this.emitStatusUpdate();
        this.log('info', `Trade ${tradeId} result: ${trade.result}, profit: ${trade.profit}`);
    }
    emitStatusUpdate() {
        this.status.uptime = this.status.startTime ? Date.now() - this.status.startTime : 0;
        this.emit('status-update', { ...this.status });
    }
    log(level, message) {
        const logEntry = {
            level,
            message,
            timestamp: Date.now()
        };
        this.emit('log', logEntry);
        console.log(`[${level.toUpperCase()}] ${message}`);
    }
    getStatus() {
        return { ...this.status };
    }
    updateSettings(settings) {
        this.config.settings = { ...settings };
        this.log('info', 'Settings updated');
    }
    async checkIfInDemoMode() {
        if (!this.page)
            return false;
        try {
            // Check for the demo mode indicator using Playwright selectors
            // Look for the specific demo indicator in the balance info block
            const balanceLabelElement = await this.page.$('.balance-info-block__label');
            if (balanceLabelElement) {
                const labelText = await balanceLabelElement.textContent();
                if (labelText && (labelText.includes('Demo') || labelText.includes('QT Demo'))) {
                    this.log('info', `Demo mode detected: Found "${labelText.trim()}" in balance label`);
                    return true;
                }
            }
            // Alternative selectors for demo mode detection
            const demoIndicators = [
                '.demo-account-indicator',
                '[data-account-type="demo"]',
                '.balance-item[data-type="demo"]'
            ];
            for (const selector of demoIndicators) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        const text = await element.textContent();
                        if (text && (text.includes('Demo') || text.includes('QT Demo'))) {
                            this.log('info', `Demo mode detected: Found "${text.trim()}" with selector ${selector}`);
                            return true;
                        }
                    }
                }
                catch (e) {
                    // Continue checking
                }
            }
            this.log('info', 'Demo mode check result: false (not in demo mode)');
            return false;
        }
        catch (error) {
            this.log('warn', `Error checking demo mode: ${error}`);
            return false;
        }
    }
    async switchToDemoAccount() {
        if (!this.page)
            return;
        try {
            this.log('info', 'Attempting to switch to demo account...');
            // Common selectors for demo account switch
            const demoSelectors = [
                '.right-block__item.js-drop-down-modal-open[data-modal-id="balance"]',
                '[data-mode="demo"]',
                '.demo-btn',
                '.demo-account',
                '.account-demo',
                'button[contains(text(), "Demo")]',
                '[title*="demo" i]',
                '[aria-label*="demo" i]',
                '.balance-switcher .demo',
                '.account-switcher .demo'
            ];
            const demoElement = '.balance-item[data-type="demo"]';
            let demoButton = null;
            let demoAccountItem = null;
            try {
                demoAccountItem = await this.page.$(demoElement);
                if (demoAccountItem) {
                    this.log('info', `Found demo account item with selector: ${demoElement}`);
                }
            }
            catch (e) {
                this.log('warn', `Demo account item not found with selector: ${demoElement}`);
            }
            for (const selector of demoSelectors) {
                try {
                    demoButton = await this.page.$(selector);
                    if (demoButton) {
                        this.log('info', `Found demo button with selector: ${selector}`);
                        break;
                    }
                }
                catch (e) {
                    // Continue to next selector
                }
            }
            if (demoButton) {
                await demoButton.click();
                this.log('info', 'Clicked demo account button');
                await this.page.waitForTimeout(2000); // Wait for switch
                if (demoAccountItem) {
                    await demoAccountItem.click();
                    this.log('info', 'Clicked demo account item');
                    await this.page.waitForTimeout(2000); // Wait for switch
                }
            }
            else {
                this.log('warn', 'Demo account button not found. You may already be in demo mode.');
            }
        }
        catch (error) {
            this.log('warn', `Could not switch to demo account: ${error}`);
        }
    }
    async selectTradingAsset() {
        if (!this.page)
            return;
        try {
            this.log('info', 'Attempting to select trading asset...');
            // Try to find asset selector
            const assetSelectors = [
                '.asset-select',
                '.asset-selector',
                '.currency-select',
                '.pair-select',
                '[data-testid="asset-select"]',
                '.trading-asset',
                '.asset-dropdown',
                '.asset-picker'
            ];
            let assetSelectionToggle = null;
            let assetSelector = null;
            let assetType = 'currency';
            let asset = 'AUD/CAD OTC';
            for (const selector of assetSelectors) {
                try {
                    assetSelector = await this.page.$(selector);
                    if (assetSelector) {
                        this.log('info', `Found asset selector with: ${selector}`);
                        break;
                    }
                }
                catch (e) {
                    // Continue to next selector
                }
            }
            if (assetSelector) {
                await assetSelector.click();
                await this.page.waitForTimeout(1000);
                const assetPanelToggle = 'a.fav-panel-switcher';
                const assetsBlock = '.assets-block';
                try {
                    assetSelectionToggle = await this.page.$(assetPanelToggle);
                    if (assetSelectionToggle) {
                        await assetSelectionToggle.click();
                        this.log('info', 'Clicked asset panel toggle');
                        // Make sure that assetsBlock is visible
                        await this.page.waitForSelector(assetsBlock, { state: 'visible' });
                        await this.page.waitForTimeout(500);
                    }
                }
                catch (e) {
                    console.log(`Could not find asset panel toggle: ${e}`);
                }
                const assetTypes = ['currency', 'cryptocurrency', 'commodity', 'stock', 'index'];
                if (assetTypes.includes(assetType)) {
                    // Click on the asset type
                    const assetTypeSelector = `.assets-block__nav-item assets-block__nav-item--${assetType}`;
                    try {
                        const assetTypeElement = await this.page.$(assetTypeSelector);
                        if (assetTypeElement) {
                            await assetTypeElement.click();
                            this.log('info', `Clicked asset type: ${assetType}`);
                            await this.page.waitForTimeout(1000);
                        }
                    }
                    catch (e) {
                        console.log(`Could not find asset type: ${e}`);
                    }
                    // Select an asset
                    const assetSelector = '.assets-block__body .alist__item';
                    try {
                        const assetItem = this.page.locator(assetSelector).filter({ hasText: asset }).first();
                        await assetItem.click();
                        this.log('info', `Selected asset: ${asset}`);
                        await this.page.waitForTimeout(500);
                        // Close the asset selection window
                        await assetSelectionToggle?.click();
                    }
                    catch (e) {
                        console.log(`Could not find asset: ${e}`);
                    }
                }
            }
        }
        catch (error) {
            this.log('warn', `Could not select trading asset: ${error}`);
        }
    }
    async waitForTradingInterface() {
        if (!this.page)
            return;
        try {
            this.log('info', 'Waiting for trading interface to be ready...');
            // Wait for essential trading elements
            const tradingSelectors = [
                '.chart, .trading-chart, .price-chart',
                '.call-btn, .put-btn, .high-btn, .low-btn, .up-btn, .down-btn',
                '.current-price, .price-display, .asset-price'
            ];
            // Wait for at least one element from each category
            for (const selectorGroup of tradingSelectors) {
                try {
                    await this.page.waitForSelector(selectorGroup, { timeout: 10000 });
                    this.log('info', `Found trading element: ${selectorGroup}`);
                }
                catch (e) {
                    this.log('warn', `Trading element not found: ${selectorGroup}`);
                }
            }
            // Additional wait for everything to settle
            await this.page.waitForTimeout(3000);
            this.log('info', 'Trading interface appears to be ready');
        }
        catch (error) {
            this.log('warn', `Trading interface setup warning: ${error}`);
        }
    }
    async debugPriceElements() {
        if (!this.page)
            return;
        try {
            // Only run debug once every 30 seconds to avoid spam
            const now = Date.now();
            if (this.lastDebugTime && now - this.lastDebugTime < 30000)
                return;
            this.lastDebugTime = now;
            this.log('info', 'Debugging: Looking for potential price elements...');
            // Look for elements that might contain numeric values (potential prices)
            const potentialPriceElements = await this.page.$$eval('*', elements => {
                return elements
                    .filter(el => {
                    const text = el.textContent?.trim() || '';
                    // Look for elements with numeric content that could be prices
                    return /^\d+\.?\d*$/.test(text) && text.length >= 3 && text.length <= 10;
                })
                    .slice(0, 10) // Limit to first 10 matches
                    .map(el => ({
                    text: el.textContent?.trim(),
                    tagName: el.tagName,
                    className: el.className,
                    id: el.id
                }));
            });
            if (potentialPriceElements.length > 0) {
                this.log('info', `Found potential price elements: ${JSON.stringify(potentialPriceElements, null, 2)}`);
            }
            else {
                this.log('warn', 'No potential price elements found');
            }
        }
        catch (error) {
            this.log('warn', `Debug error: ${error}`);
        }
    }
}
exports.TradingBot = TradingBot;
//# sourceMappingURL=TradingBot.js.map