import { TradingSettings } from '../../../shared/types';
export declare class BotManager {
    private tradingBot;
    private eventCallbacks;
    constructor();
    on(event: string, callback: Function): void;
    emit(event: string, data: any): void;
    startBot(settings: TradingSettings): Promise<{
        success: boolean;
        message: string;
    }>;
    stopBot(): Promise<{
        success: boolean;
        message: string;
    }>;
    getBotStatus(): any;
    updateSettings(settings: TradingSettings): {
        success: boolean;
        message: string;
    };
}
//# sourceMappingURL=BotManager.d.ts.map