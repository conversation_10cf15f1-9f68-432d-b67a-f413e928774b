import { TradingSettings } from '../../../shared/types';
export declare class BotManager {
    private tradingBot;
    private browserInstance;
    private browserPage;
    private eventCallbacks;
    private browserInitialized;
    private userLoggedIn;
    constructor();
    on(event: string, callback: Function): void;
    emit(event: string, data: any): void;
    initializeBrowser(): Promise<{
        success: boolean;
        message: string;
    }>;
    checkLoginStatus(): Promise<{
        success: boolean;
        loggedIn: boolean;
        message: string;
    }>;
    closeBrowser(): Promise<{
        success: boolean;
        message: string;
    }>;
    getBrowserStatus(): {
        initialized: boolean;
        loggedIn: boolean;
    };
    startBot(settings: TradingSettings): Promise<{
        success: boolean;
        message: string;
    }>;
    stopBot(): Promise<{
        success: boolean;
        message: string;
    }>;
    getBotStatus(): any;
    updateSettings(settings: TradingSettings): {
        success: boolean;
        message: string;
    };
}
//# sourceMappingURL=BotManager.d.ts.map