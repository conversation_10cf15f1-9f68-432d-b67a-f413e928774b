{"version": 3, "file": "BotManager.js", "sourceRoot": "", "sources": ["../../../../src/BotManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4B;AAC5B,uCAA8B;AAG9B,yDAAyD;AACzD,IAAI,UAAU,GAAQ,IAAI,CAAA;AAC1B,IAAI,SAAS,GAAQ,IAAI,CAAA;AAEzB,6BAA6B;AAC7B,KAAK,UAAU,OAAO;IACrB,IAAI,CAAC,UAAU,EAAE,CAAC;QACjB,IAAI,CAAC;YACJ,gDAAgD;YAChD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,+CAA+C,CAAC,CAAA;YAC9F,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,aAAa,CAAC,CAAA;YACjE,MAAM,SAAS,GAAG,yBAAa,aAAa,uCAAC,CAAA;YAC7C,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;YAC3D,UAAU,GAAG,SAAS,CAAC,UAAU,CAAA;YACjC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAA;YAC/B,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,UAAU,CAAC,CAAA;YAC7C,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,SAAS,CAAC,CAAA;YAE3C,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAA;YAC/D,CAAC;QACF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAA;YAClD,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YAC3E,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAA;YAC7C,MAAM,IAAI,KAAK,CAAC,6BAA6B,YAAY,EAAE,CAAC,CAAA;QAC7D,CAAC;IACF,CAAC;AACF,CAAC;AAED,MAAa,UAAU;IAQtB;QAPQ,eAAU,GAAQ,IAAI,CAAA;QACtB,oBAAe,GAAQ,IAAI,CAAA;QAC3B,gBAAW,GAAQ,IAAI,CAAA;QACvB,mBAAc,GAAkC,EAAE,CAAA;QAClD,uBAAkB,GAAY,KAAK,CAAA;QACnC,iBAAY,GAAY,KAAK,CAAA;QAGpC,0BAA0B;QAC1B,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAC/B,CAAC;IAED,wBAAwB;IACxB,EAAE,CAAC,KAAa,EAAE,QAAkB;QACnC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;QAChC,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC1C,CAAC;IAED,IAAI,CAAC,KAAa,EAAE,IAAS;QAC5B,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;QAC/D,CAAC;IACF,CAAC;IAED,KAAK,CAAC,iBAAiB;QACtB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAA;QACjE,CAAC;QAED,IAAI,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAA;YACtC,MAAM,OAAO,EAAE,CAAA;YAEf,MAAM,EAAE,QAAQ,EAAE,GAAG,wDAAa,YAAY,GAAC,CAAA;YAE/C,6DAA6D;YAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,cAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,iBAAiB,CAAC,CAAA;YACzE,IAAI,CAAC,eAAe,GAAG,MAAM,QAAQ,CAAC,uBAAuB,CAAC,WAAW,EAAE;gBAC1E,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,IAAI,CAAC,uBAAuB;aACtC,CAAC,CAAA;YAEF,yCAAyC;YACzC,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;YAC1C,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAA;YAErF,4BAA4B;YAC5B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;YAC7C,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAA;YAEvF,6BAA6B;YAC7B,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAA;YAE/E,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA;YAE9B,qCAAqC;YACrC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YAE7B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;YAC/C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,4DAA4D,EAAE,CAAA;QAChG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;YACrD,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;YACzB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,iCAAiC,KAAK,EAAE,EAAE,CAAA;QAC7E,CAAC;IACF,CAAC;IAED,KAAK,CAAC,gBAAgB;QACrB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACvB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAA;QAC/E,CAAC;QAED,IAAI,CAAC;YACJ,6BAA6B;YAC7B,MAAM,eAAe,GAAG;gBACvB,YAAY;gBACZ,kBAAkB;gBAClB,eAAe;gBACf,YAAY;gBACZ,oBAAoB;gBACpB,eAAe;gBACf,eAAe;aACf,CAAA;YAED,IAAI,QAAQ,GAAG,KAAK,CAAA;YACpB,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE,CAAC;gBACxC,IAAI,CAAC;oBACJ,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;oBAClD,IAAI,OAAO,EAAE,CAAC;wBACb,QAAQ,GAAG,IAAI,CAAA;wBACf,MAAK;oBACN,CAAC;gBACF,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACZ,oBAAoB;gBACrB,CAAC;YACF,CAAC;YAED,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAA;YAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,sBAAsB,CAAA;YAEvE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAA;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;YACrD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,iCAAiC,KAAK,EAAE,EAAE,CAAA;QAC9F,CAAC;IACF,CAAC;IAED,KAAK,CAAC,YAAY;QACjB,IAAI,CAAC;YACJ,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;gBAClC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;gBAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACxB,CAAC;YACD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;YAC/B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;YACzB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAA;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YAChD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,4BAA4B,KAAK,EAAE,EAAE,CAAA;QACxE,CAAC;IACF,CAAC;IAED,gBAAgB;QACf,OAAO;YACN,WAAW,EAAE,IAAI,CAAC,kBAAkB;YACpC,QAAQ,EAAE,IAAI,CAAC,YAAY;SAC3B,CAAA;IACF,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,QAAyB;QACvC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC;YAC9D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAA;QAC7D,CAAC;QAED,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAA;QACtE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACxB,wBAAwB;YACxB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YAChD,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;gBAC1B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAA;YAC3E,CAAC;QACF,CAAC;QAED,IAAI,CAAC;YACJ,8BAA8B;YAC9B,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA;YACpC,MAAM,OAAO,EAAE,CAAA;YACf,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;YAE7C,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,QAAQ,CAAC,CAAA;YAEpD,gDAAgD;YAChD,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;YACvC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,QAAQ,EAAE;gBACrC,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,cAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,iBAAiB,CAAC;gBAClE,eAAe,EAAE,IAAI,CAAC,eAAe;gBACrC,YAAY,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAA;YACF,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;YAEnC,2CAA2C;YAC3C,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,MAAW,EAAE,EAAE;gBACnD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAA;YACnC,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,SAAc,EAAE,EAAE;gBACrD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAA;YACrC,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,MAAW,EAAE,EAAE;gBAClD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAA;YAClC,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,QAAa,EAAE,EAAE;gBAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAA;gBACxE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;YAC3B,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE;gBAC1C,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;gBACnC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YAC1B,CAAC,CAAC,CAAA;YAEF,gBAAgB;YAChB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;YAC9B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAA;YAC7B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAA;YAExC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAA;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;YAC5C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,wBAAwB,KAAK,EAAE,EAAE,CAAA;QACpE,CAAC;IACF,CAAC;IAED,KAAK,CAAC,OAAO;QACZ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACtB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAA;QAC5D,CAAC;QAED,IAAI,CAAC;YACJ,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAA;YAC5B,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAA;YACpC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YAEtB,mCAAmC;YACnC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAA;YAC1D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAA;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAA;YAC3C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,uBAAuB,KAAK,EAAE,EAAE,CAAA;QACnE,CAAC;IACF,CAAC;IAED,YAAY;QACX,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACtB,OAAO;gBACN,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,CAAC;gBACf,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,CAAC;gBACZ,OAAO,EAAE,CAAC;gBACV,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,IAAI;aACf,CAAA;QACF,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAA;IACnC,CAAC;IAED,cAAc,CAAC,QAAyB;QACvC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;YACxC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAA;QACnE,CAAC;QACD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAA;IAC5D,CAAC;CACD;AAvPD,gCAuPC"}