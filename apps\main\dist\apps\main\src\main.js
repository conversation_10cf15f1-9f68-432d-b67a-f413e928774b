"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const BotManager_1 = require("./BotManager");
const fs = __importStar(require("fs"));
// Simple settings manager
class SettingsManager {
    constructor() {
        this.defaultSettings = {
            windowBounds: { width: 1200, height: 800 },
            tradingSettings: {
                threshold: 0.02,
                tradeAmount: 1,
                autoTrade: false,
                maxTrades: 10
            }
        };
        this.settingsPath = path.join(electron_1.app.getPath('userData'), 'settings.json');
    }
    get(key) {
        try {
            const settings = JSON.parse(fs.readFileSync(this.settingsPath, 'utf8'));
            return settings[key] || this.defaultSettings[key];
        }
        catch {
            return this.defaultSettings[key];
        }
    }
    set(key, value) {
        try {
            let settings = {};
            try {
                settings = JSON.parse(fs.readFileSync(this.settingsPath, 'utf8'));
            }
            catch {
                // File doesn't exist or is invalid, start with empty object
            }
            settings[key] = value;
            fs.writeFileSync(this.settingsPath, JSON.stringify(settings, null, 2));
        }
        catch (error) {
            console.error('Failed to save settings:', error);
        }
    }
}
const settings = new SettingsManager();
let mainWindow = null;
let botManager = null;
const createWindow = () => {
    // Get stored window bounds or use defaults
    const windowBounds = settings.get('windowBounds');
    // Create the browser window
    mainWindow = new electron_1.BrowserWindow({
        width: windowBounds.width,
        height: windowBounds.height,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, '../assets/icon.png'), // We'll add this later
        title: 'Pocket Option Trading Bot',
        minWidth: 800,
        minHeight: 600,
        show: false // Don't show until ready
    });
    // Load the renderer app
    const isDev = !electron_1.app.isPackaged;
    if (isDev) {
        mainWindow.loadURL('http://localhost:5173');
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../../renderer/dist/index.html'));
    }
    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow?.show();
    });
    // Save window bounds on resize/move
    mainWindow.on('resize', () => {
        if (mainWindow) {
            settings.set('windowBounds', mainWindow.getBounds());
        }
    });
    mainWindow.on('move', () => {
        if (mainWindow) {
            settings.set('windowBounds', mainWindow.getBounds());
        }
    });
    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
};
// App event handlers
electron_1.app.whenReady().then(() => {
    createWindow();
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
// IPC handlers for communication with renderer
electron_1.ipcMain.handle('get-settings', () => {
    return settings.get('tradingSettings');
});
electron_1.ipcMain.handle('save-settings', (_, tradingSettings) => {
    settings.set('tradingSettings', tradingSettings);
    return true;
});
electron_1.ipcMain.handle('show-error-dialog', (_, title, content) => {
    if (mainWindow) {
        electron_1.dialog.showErrorBox(title, content);
    }
});
electron_1.ipcMain.handle('show-info-dialog', async (_, title, content) => {
    if (mainWindow) {
        const result = await electron_1.dialog.showMessageBox(mainWindow, {
            type: 'info',
            title,
            message: content,
            buttons: ['OK']
        });
        return result;
    }
});
// Initialize bot manager
electron_1.app.whenReady().then(() => {
    botManager = new BotManager_1.BotManager();
    // Set up event forwarding from bot manager to renderer
    botManager.on('status-update', (status) => {
        mainWindow?.webContents.send('bot-status-update', status);
    });
    botManager.on('price-update', (priceData) => {
        mainWindow?.webContents.send('price-update', priceData);
    });
    botManager.on('trade-result', (result) => {
        mainWindow?.webContents.send('trade-result', result);
    });
    botManager.on('error', (error) => {
        console.error('[BOT ERROR]', error);
        mainWindow?.webContents.send('bot-error', error);
    });
    botManager.on('log', (logEntry) => {
        console.log(`[BOT ${logEntry.level.toUpperCase()}] ${logEntry.message}`);
        // Send important log messages to the UI
        if (logEntry.level === 'info' &&
            (logEntry.message.includes('Please sign in') ||
                logEntry.message.includes('Waiting for page') ||
                logEntry.message.includes('Trading interface detected') ||
                logEntry.message.includes('Navigating to'))) {
            mainWindow?.webContents.send('bot-status-message', logEntry.message);
        }
    });
});
// Bot control IPC handlers
electron_1.ipcMain.handle('start-bot', async (_, settings) => {
    if (!botManager) {
        return { success: false, message: 'Bot manager not initialized' };
    }
    return await botManager.startBot(settings);
});
electron_1.ipcMain.handle('stop-bot', async () => {
    if (!botManager) {
        return { success: false, message: 'Bot manager not initialized' };
    }
    return await botManager.stopBot();
});
electron_1.ipcMain.handle('get-bot-status', () => {
    if (!botManager) {
        return {
            isRunning: false,
            currentPrice: 0,
            tradesCount: 0,
            winCount: 0,
            lossCount: 0,
            winRate: 0,
            totalProfit: 0,
            lastTrade: null
        };
    }
    return botManager.getBotStatus();
});
electron_1.ipcMain.handle('update-bot-settings', (_, settings) => {
    if (!botManager) {
        return { success: false, message: 'Bot manager not initialized' };
    }
    return botManager.updateSettings(settings);
});
//# sourceMappingURL=main.js.map