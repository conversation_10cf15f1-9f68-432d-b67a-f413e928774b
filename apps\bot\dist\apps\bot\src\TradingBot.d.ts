import { EventEmitter } from 'events';
import { BotConfig, BotStatus, TradingSettings } from '../../../shared/types';
export declare class TradingBot extends EventEmitter {
    private browser;
    private page;
    private isRunning;
    private config;
    private status;
    private priceHistory;
    private trades;
    private priceCheckInterval;
    constructor(config: BotConfig);
    start(): Promise<void>;
    stop(): Promise<void>;
    private startPriceMonitoring;
    private checkPrice;
    private evaluateTradeOpportunity;
    private executeTrade;
    private checkTradeResult;
    private emitStatusUpdate;
    private log;
    getStatus(): BotStatus;
    updateSettings(settings: TradingSettings): void;
    private checkIfInDemoMode;
    private switchToDemoAccount;
    private selectTradingAsset;
    private waitForTradingInterface;
    private debugPriceElements;
    private lastDebugTime?;
}
//# sourceMappingURL=TradingBot.d.ts.map