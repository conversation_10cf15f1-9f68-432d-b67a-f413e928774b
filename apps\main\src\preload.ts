import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

// Define the API that will be exposed to the renderer process
const electronAPI = {
	// Settings management
	getSettings: () => ipcRenderer.invoke('get-settings'),
	saveSettings: (settings: any) => ipcRenderer.invoke('save-settings', settings),

	// Dialog utilities
	showErrorDialog: (title: string, content: string) => ipcRenderer.invoke('show-error-dialog', title, content),
	showInfoDialog: (title: string, content: string) => ipcRenderer.invoke('show-info-dialog', title, content),

	// Bot control
	startBot: (settings: any) => ipcRenderer.invoke('start-bot', settings),
	stopBot: () => ipcRenderer.invoke('stop-bot'),
	getBotStatus: () => ipcRenderer.invoke('get-bot-status'),
	updateBotSettings: (settings: any) => ipcRenderer.invoke('update-bot-settings', settings),

	// Browser control
	initializeBrowser: () => ipcRenderer.invoke('initialize-browser'),
	checkLoginStatus: () => ipcRenderer.invoke('check-login-status'),
	closeBrowser: () => ipcRenderer.invoke('close-browser'),
	getBrowserStatus: () => ipcRenderer.invoke('get-browser-status'),

	// Bot status updates (for real-time data)
	onBotStatusUpdate: (callback: (status: any) => void) => {
		ipcRenderer.on('bot-status-update', (_, status) => callback(status))
	},

	onPriceUpdate: (callback: (price: number) => void) => {
		ipcRenderer.on('price-update', (_, price) => callback(price))
	},

	onTradeResult: (callback: (result: any) => void) => {
		ipcRenderer.on('trade-result', (_, result) => callback(result))
	},

	onBotStatusMessage: (callback: (message: string) => void) => {
		ipcRenderer.on('bot-status-message', (_, message) => callback(message))
	},

	// Remove listeners
	removeAllListeners: (channel: string) => {
		ipcRenderer.removeAllListeners(channel)
	}
}

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// Type definitions for TypeScript (will be used in renderer)
export type ElectronAPI = typeof electronAPI
